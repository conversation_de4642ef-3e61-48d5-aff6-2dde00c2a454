import { useState } from "react";
import { Form, Input, Button, Typography, Divider } from "antd";
import { UserOutlined, LockOutlined, MailOutlined } from "@ant-design/icons";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../../contexts";
import { ROUTES } from "../../common/routes";

const { Title, Text } = Typography;

interface SignupFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

function Signup() {
  // HOOKS
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const { signup } = useAuth();
  const navigate = useNavigate();

  // EVENT HANDLERS
  const handleSubmit = async (values: SignupFormData) => {
    setIsLoading(true);
    try {
      await signup(values.email, values.password, values.name);
      navigate(ROUTES.DASHBOARD, { replace: true });
    } catch (error) {
      // Error is handled by the auth context
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <Title level={2} className="text-center mb-6">
        Create Account
      </Title>

      <Form
        form={form}
        name="signup"
        onFinish={handleSubmit}
        layout="vertical"
        size="large"
        autoComplete="off"
      >
        <Form.Item
          name="name"
          label="Full Name"
          rules={[
            { required: true, message: "Please enter your full name!" },
            { min: 2, message: "Name must be at least 2 characters!" },
          ]}
        >
          <Input prefix={<UserOutlined />} placeholder="Enter your full name" />
        </Form.Item>

        <Form.Item
          name="email"
          label="Email"
          rules={[
            { required: true, message: "Please enter your email!" },
            { type: "email", message: "Please enter a valid email!" },
          ]}
        >
          <Input prefix={<MailOutlined />} placeholder="Enter your email" />
        </Form.Item>

        <Form.Item
          name="password"
          label="Password"
          rules={[
            { required: true, message: "Please enter your password!" },
            { min: 6, message: "Password must be at least 6 characters!" },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Enter your password"
          />
        </Form.Item>

        <Form.Item
          name="confirmPassword"
          label="Confirm Password"
          dependencies={["password"]}
          rules={[
            { required: true, message: "Please confirm your password!" },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("password") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error("Passwords do not match!"));
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Confirm your password"
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={isLoading}
            className="w-full"
          >
            Create Account
          </Button>
        </Form.Item>
      </Form>

      <Divider>or</Divider>

      <div className="text-center">
        <Text>Already have an account? </Text>
        <Link to={ROUTES.LOGIN} className="text-blue-600 hover:text-blue-800">
          Sign in here
        </Link>
      </div>
    </div>
  );
}

export default Signup;
