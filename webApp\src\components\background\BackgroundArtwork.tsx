import { useEffect, useState } from "react";
import { useTheme } from "../../contexts";

interface ArtworkItem {
  id: string;
  x: number;
  y: number;
  size: number;
  rotation: number;
  icon: string;
  opacity: number;
}

interface BackgroundArtworkProps {
  enabled: boolean;
}

// E-filing related SVG icons
const FILING_ICONS = [
  // Document icon
  `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
    <polyline points="14,2 14,8 20,8"/>
    <line x1="16" y1="13" x2="8" y2="13"/>
    <line x1="16" y1="17" x2="8" y2="17"/>
    <polyline points="10,9 9,9 8,9"/>
  </svg>`,

  // Folder icon
  `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
    <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
  </svg>`,

  // Stamp/Seal icon
  `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
    <circle cx="12" cy="12" r="10"/>
    <path d="M12 6v6l4 2"/>
  </svg>`,

  // Archive icon
  `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
    <polyline points="21,8 21,21 3,21 3,8"/>
    <rect x="1" y="3" width="22" height="5"/>
    <line x1="10" y1="12" x2="14" y2="12"/>
  </svg>`,

  // File text icon
  `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
    <polyline points="14,2 14,8 20,8"/>
    <line x1="16" y1="13" x2="8" y2="13"/>
    <line x1="16" y1="17" x2="8" y2="17"/>
    <line x1="10" y1="9" x2="8" y2="9"/>
  </svg>`,

  // Clipboard icon
  `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
    <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
  </svg>`,
];

const generateRandomArtwork = (): ArtworkItem => {
  return {
    id: Math.random().toString(36).substring(2, 11),
    x: Math.random() * 90 + 5, // 5-95% to avoid edges
    y: Math.random() * 90 + 5, // 5-95% to avoid edges
    size: Math.random() * 80 + 30, // 30-110px - wider range
    rotation: Math.random() * 360,
    icon: FILING_ICONS[Math.floor(Math.random() * FILING_ICONS.length)],
    opacity: Math.random() * 0.25 + 0.15, // 0.15-0.4 opacity - much more visible
  };
};

function BackgroundArtwork({ enabled }: BackgroundArtworkProps) {
  const { mode } = useTheme();
  const [artworkItems, setArtworkItems] = useState<ArtworkItem[]>([]);

  useEffect(() => {
    if (enabled) {
      // Generate 3-10 random artwork items
      const count = Math.floor(Math.random() * 8) + 3; // 3-10 items
      const newItems = Array.from({ length: count }, generateRandomArtwork);
      setArtworkItems(newItems);
    } else {
      setArtworkItems([]);
    }
  }, [enabled]);

  if (!enabled || artworkItems.length === 0) {
    return null;
  }

  const iconColor =
    mode === "dark" ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.3)";

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {artworkItems.map((item) => (
        <div
          key={item.id}
          className="absolute"
          style={{
            left: `${item.x}%`,
            top: `${item.y}%`,
            width: `${item.size}px`,
            height: `${item.size}px`,
            transform: `translate(-50%, -50%) rotate(${item.rotation}deg)`,
            opacity: item.opacity,
            color: iconColor,
          }}
          dangerouslySetInnerHTML={{ __html: item.icon }}
        />
      ))}
    </div>
  );
}

export default BackgroundArtwork;
