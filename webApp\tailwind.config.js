/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      backgroundImage: {
        // Linear gradients
        'gradient-indigo-violet': 'linear-gradient(135deg, rgb(99 102 241 / 0.6), rgb(168 85 247 / 0.6))',
        'gradient-blue-pink': 'linear-gradient(135deg, rgb(59 130 246 / 0.6), rgb(236 72 153 / 0.6))',
        'gradient-violet-red': 'linear-gradient(135deg, rgb(139 92 246 / 0.6), rgb(239 68 68 / 0.6))',
        'gradient-emerald': 'linear-gradient(135deg, rgb(16 185 129 / 0.6), rgb(5 150 105 / 0.6))',
        'gradient-amber-red': 'linear-gradient(135deg, rgb(251 191 36 / 0.6), rgb(245 101 101 / 0.6))',
        'gradient-green-blue': 'linear-gradient(135deg, rgb(34 197 94 / 0.6), rgb(59 130 246 / 0.6))',
        'gradient-purple-orange': 'linear-gradient(135deg, rgb(147 51 234 / 0.6), rgb(251 146 60 / 0.6))',
        'gradient-cyan-purple': 'linear-gradient(135deg, rgb(6 182 212 / 0.6), rgb(168 85 247 / 0.6))',
        
        // Radial gradients
        'radial-indigo-violet': 'radial-gradient(circle, rgb(99 102 241 / 0.6) 0%, rgb(168 85 247 / 0.6) 100%)',
        'radial-green-blue': 'radial-gradient(circle, rgb(34 197 94 / 0.6) 0%, rgb(59 130 246 / 0.6) 100%)',
        'radial-pink-amber': 'radial-gradient(circle, rgb(236 72 153 / 0.6) 0%, rgb(251 191 36 / 0.6) 100%)',
        'radial-purple-cyan': 'radial-gradient(circle, rgb(147 51 234 / 0.6) 0%, rgb(6 182 212 / 0.6) 100%)',
        
        // Conic gradients
        'conic-rainbow': 'conic-gradient(from 0deg, rgb(99 102 241 / 0.6), rgb(168 85 247 / 0.6), rgb(236 72 153 / 0.6), rgb(99 102 241 / 0.6))',
        'conic-nature': 'conic-gradient(from 45deg, rgb(16 185 129 / 0.6), rgb(59 130 246 / 0.6), rgb(139 92 246 / 0.6), rgb(16 185 129 / 0.6))',
        'conic-sunset': 'conic-gradient(from 90deg, rgb(251 191 36 / 0.6), rgb(245 101 101 / 0.6), rgb(236 72 153 / 0.6), rgb(251 191 36 / 0.6))',
        
        // Multi-stop gradients
        'multi-purple': 'linear-gradient(45deg, rgb(99 102 241 / 0.6) 0%, rgb(168 85 247 / 0.6) 50%, rgb(236 72 153 / 0.6) 100%)',
        'multi-nature': 'linear-gradient(225deg, rgb(34 197 94 / 0.6) 0%, rgb(59 130 246 / 0.6) 50%, rgb(139 92 246 / 0.6) 100%)',
        'multi-warm': 'linear-gradient(135deg, rgb(251 191 36 / 0.6) 0%, rgb(245 101 101 / 0.6) 50%, rgb(236 72 153 / 0.6) 100%)',
        
        // Dark theme versions (lower opacity)
        'gradient-indigo-violet-dark': 'linear-gradient(135deg, rgb(99 102 241 / 0.4), rgb(168 85 247 / 0.4))',
        'gradient-blue-pink-dark': 'linear-gradient(135deg, rgb(59 130 246 / 0.4), rgb(236 72 153 / 0.4))',
        'gradient-violet-red-dark': 'linear-gradient(135deg, rgb(139 92 246 / 0.4), rgb(239 68 68 / 0.4))',
        'gradient-emerald-dark': 'linear-gradient(135deg, rgb(16 185 129 / 0.4), rgb(5 150 105 / 0.4))',
        'gradient-amber-red-dark': 'linear-gradient(135deg, rgb(251 191 36 / 0.4), rgb(245 101 101 / 0.4))',
        'gradient-green-blue-dark': 'linear-gradient(135deg, rgb(34 197 94 / 0.4), rgb(59 130 246 / 0.4))',
        'gradient-purple-orange-dark': 'linear-gradient(135deg, rgb(147 51 234 / 0.4), rgb(251 146 60 / 0.4))',
        'gradient-cyan-purple-dark': 'linear-gradient(135deg, rgb(6 182 212 / 0.4), rgb(168 85 247 / 0.4))',
        
        'radial-indigo-violet-dark': 'radial-gradient(circle, rgb(99 102 241 / 0.4) 0%, rgb(168 85 247 / 0.4) 100%)',
        'radial-green-blue-dark': 'radial-gradient(circle, rgb(34 197 94 / 0.4) 0%, rgb(59 130 246 / 0.4) 100%)',
        'radial-pink-amber-dark': 'radial-gradient(circle, rgb(236 72 153 / 0.4) 0%, rgb(251 191 36 / 0.4) 100%)',
        'radial-purple-cyan-dark': 'radial-gradient(circle, rgb(147 51 234 / 0.4) 0%, rgb(6 182 212 / 0.4) 100%)',
        
        'conic-rainbow-dark': 'conic-gradient(from 0deg, rgb(99 102 241 / 0.4), rgb(168 85 247 / 0.4), rgb(236 72 153 / 0.4), rgb(99 102 241 / 0.4))',
        'conic-nature-dark': 'conic-gradient(from 45deg, rgb(16 185 129 / 0.4), rgb(59 130 246 / 0.4), rgb(139 92 246 / 0.4), rgb(16 185 129 / 0.4))',
        'conic-sunset-dark': 'conic-gradient(from 90deg, rgb(251 191 36 / 0.4), rgb(245 101 101 / 0.4), rgb(236 72 153 / 0.4), rgb(251 191 36 / 0.4))',
        
        'multi-purple-dark': 'linear-gradient(45deg, rgb(99 102 241 / 0.4) 0%, rgb(168 85 247 / 0.4) 50%, rgb(236 72 153 / 0.4) 100%)',
        'multi-nature-dark': 'linear-gradient(225deg, rgb(34 197 94 / 0.4) 0%, rgb(59 130 246 / 0.4) 50%, rgb(139 92 246 / 0.4) 100%)',
        'multi-warm-dark': 'linear-gradient(135deg, rgb(251 191 36 / 0.4) 0%, rgb(245 101 101 / 0.4) 50%, rgb(236 72 153 / 0.4) 100%)',
      },
    },
  },
  plugins: [],
}
