import { type ReactNode } from "react";
import { Navigate, useLocation, Outlet } from "react-router-dom";
import { Spin } from "antd";
import { ROUTES } from "../common/routes";
import { useAuth } from "../contexts";

interface ProtectedRouteProps {
  children?: ReactNode;
}

function ProtectedRoute({ children }: ProtectedRouteProps) {
  const location = useLocation();
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" />
      </div>
    );
  }

  if (!isAuthenticated) {
    // Redirect to login page with return url
    return <Navigate to={ROUTES.LOGIN} state={{ from: location }} replace />;
  }

  return <>{children || <Outlet />}</>;
}

export default ProtectedRoute;
