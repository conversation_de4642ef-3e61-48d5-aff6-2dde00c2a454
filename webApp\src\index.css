@import "tailwindcss";

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
}

/* Glass morphism animations */
@keyframes float {
  0%, 100% {
    transform: translate(-50%, -50%) translateY(0px) scale(1);
  }
  25% {
    transform: translate(-50%, -50%) translateY(-20px) scale(1.05);
  }
  50% {
    transform: translate(-50%, -50%) translateY(10px) scale(0.95);
  }
  75% {
    transform: translate(-50%, -50%) translateY(-15px) scale(1.02);
  }
}

.animate-float {
  animation: float linear infinite;
}

/* Glass morphism backdrop filter support */
.glass-morphism {
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-morphism-dark {
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(5px);
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

