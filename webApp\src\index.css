@import "tailwindcss";

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
}

/* Glass morphism animations - enhanced movement */
@keyframes float {
  0% {
    transform: translate(-50%, -50%) translateX(0px) translateY(0px) scale(1) rotate(0deg);
  }
  20% {
    transform: translate(-50%, -50%) translateX(30px) translateY(-40px) scale(1.08) rotate(5deg);
  }
  40% {
    transform: translate(-50%, -50%) translateX(-25px) translateY(20px) scale(0.92) rotate(-3deg);
  }
  60% {
    transform: translate(-50%, -50%) translateX(40px) translateY(-10px) scale(1.05) rotate(8deg);
  }
  80% {
    transform: translate(-50%, -50%) translateX(-30px) translateY(-25px) scale(0.98) rotate(-6deg);
  }
  100% {
    transform: translate(-50%, -50%) translateX(0px) translateY(0px) scale(1) rotate(0deg);
  }
}

.animate-float {
  animation: float ease-in-out infinite;
}

/* Glass morphism backdrop filter support */
.glass-morphism {
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-morphism-dark {
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(5px);
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Smooth blob animation - no jumping, continuous movement */
@keyframes float {
  0% {
    transform: translate(-50%, -50%) translateX(0px) translateY(0px) scale(1) rotate(0deg);
  }
  25% {
    transform: translate(-50%, -50%) translateX(30px) translateY(-20px) scale(1.05) rotate(90deg);
  }
  50% {
    transform: translate(-50%, -50%) translateX(0px) translateY(-40px) scale(1.1) rotate(180deg);
  }
  75% {
    transform: translate(-50%, -50%) translateX(-30px) translateY(-20px) scale(1.05) rotate(270deg);
  }
  100% {
    transform: translate(-50%, -50%) translateX(0px) translateY(0px) scale(1) rotate(360deg);
  }
}

.animate-float {
  animation: float ease-in-out infinite;
}
