import { useState } from "react";
import { Form, Input, Button, <PERSON>po<PERSON>, Di<PERSON><PERSON>, Al<PERSON>, Space } from "antd";
import { UserOutlined, LockOutlined, MailOutlined } from "@ant-design/icons";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../../contexts";
import { ROUTES } from "../../common/routes";

const { Title, Text } = Typography;

interface LoginFormData {
  email: string;
  password: string;
}

function Login() {
  // HOOKS
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // HELPERS
  const getRedirectPath = () => {
    const from = location.state?.from?.pathname;
    return from && from !== ROUTES.LOGIN ? from : ROUTES.DASHBOARD;
  };

  // EVENT HANDLERS
  const handleSubmit = async (values: LoginFormData) => {
    setIsLoading(true);
    try {
      await login(values.email, values.password);
      navigate(getRedirectPath(), { replace: true });
    } catch (error) {
      // Error is handled by the auth context
    } finally {
      setIsLoading(false);
    }
  };

  // RENDER LOGIC
  const demoCredentials = (
    <Alert
      message="Demo Credentials"
      description="Email: <EMAIL> | Password: password"
      type="info"
      showIcon
      className="mb-6"
    />
  );

  return (
    <Space direction="vertical" size="small" className="w-full">
      <Title level={2} className="text-center">
        Welcome Back
      </Title>

      {demoCredentials}

      <Form
        form={form}
        name="login"
        onFinish={handleSubmit}
        layout="vertical"
        size="large"
        autoComplete="off"
      >
        <Form.Item
          name="email"
          label="Email"
          rules={[
            { required: true, message: "Please enter your email!" },
            { type: "email", message: "Please enter a valid email!" },
          ]}
        >
          <Input prefix={<MailOutlined />} placeholder="Enter your email" />
        </Form.Item>

        <Form.Item
          name="password"
          label="Password"
          rules={[
            { required: true, message: "Please enter your password!" },
            { min: 6, message: "Password must be at least 6 characters!" },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="Enter your password"
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={isLoading}
            className="w-full"
          >
            Sign In
          </Button>
        </Form.Item>
      </Form>

      <Divider>or</Divider>

      <Typography.Paragraph className="text-center">
        <Text>Don't have an account? </Text>
        <Link to={ROUTES.SIGNUP} className="text-blue-600 hover:text-blue-800">
          Sign up now
        </Link>
      </Typography.Paragraph>
    </Space>
  );
}

export default Login;
