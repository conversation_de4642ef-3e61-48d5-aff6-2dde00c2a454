import { useEffect, useState } from "react";
import { useTheme } from "../../contexts";

interface Blob {
  id: string;
  x: number;
  y: number;
  size: number;
  borderRadius: string;
  background: string;
  animationDuration: number;
  animationDelay: number;
  animationName: string;
}

interface AnimatedBlobsProps {
  enabled: boolean;
  animated: boolean;
}

// Light theme colors with slightly increased opacity for better visibility
const LIGHT_COLORS = [
  "linear-gradient(135deg, rgba(99, 102, 241, 0.5), rgba(168, 85, 247, 0.5))",
  "linear-gradient(135deg, rgba(59, 130, 246, 0.5), rgba(34, 197, 94, 0.5))",
  "linear-gradient(135deg, rgba(139, 92, 246, 0.5), rgba(239, 68, 68, 0.5))",
  "linear-gradient(135deg, rgba(251, 191, 36, 0.5), rgba(245, 101, 101, 0.5))",
];

// Dark theme colors
const DARK_COLORS = [
  "linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(168, 85, 247, 0.3))",
  "linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(34, 197, 94, 0.3))",
  "linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(239, 68, 68, 0.3))",
  "linear-gradient(135deg, rgba(79, 70, 229, 0.3), rgba(219, 39, 119, 0.3))",
];

const animationNames = ["float1", "float2", "float3", "float4"];

const generateRandomBlob = (isDark: boolean): Blob => {
  const colors = isDark ? DARK_COLORS : LIGHT_COLORS;
  const size = Math.random() * 350 + 200; // Size range: 200px to 550px

  const borderRadius = `${Math.random() * 40 + 40}% ${
    Math.random() * 40 + 40
  }% ${Math.random() * 40 + 40}% ${Math.random() * 40 + 40}% / ${
    Math.random() * 40 + 40
  }% ${Math.random() * 40 + 40}% ${Math.random() * 40 + 40}% ${
    Math.random() * 40 + 40
  }%`;

  return {
    id: Math.random().toString(36).substring(2, 11),
    x: Math.random() * 80 + 10,
    y: Math.random() * 80 + 10,
    size,
    borderRadius,
    background: colors[Math.floor(Math.random() * colors.length)],
    animationDuration: size / 15 + 20, // Duration between ~33s and ~53s
    animationDelay: Math.random() * 5,
    animationName:
      animationNames[Math.floor(Math.random() * animationNames.length)],
  };
};

function AnimatedBlobs({ enabled, animated }: AnimatedBlobsProps) {
  const { isDarkMode } = useTheme();
  const [blobs, setBlobs] = useState<Blob[]>([]);

  useEffect(() => {
    if (enabled) {
      // Use a smaller divisor for more blobs on larger screens
      const blobCount = Math.floor(window.innerWidth / 250);
      const newBlobs = Array.from({ length: Math.max(4, blobCount) }, () =>
        generateRandomBlob(isDarkMode)
      );
      setBlobs(newBlobs);
    } else {
      setBlobs([]);
    }
  }, [enabled, isDarkMode]);

  if (!enabled || blobs.length === 0) {
    return null;
  }

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {blobs.map((blob) => (
        <div
          key={blob.id}
          className="absolute"
          style={{
            left: `${blob.x}%`,
            top: `${blob.y}%`,
            width: `${blob.size}px`,
            height: `${blob.size}px`,
            background: blob.background,
            borderRadius: blob.borderRadius,
            filter: "blur(100px)",
            opacity: 0.8,
            animationName: animated ? blob.animationName : "none",
            animationDuration: `${blob.animationDuration}s`,
            animationDelay: `${blob.animationDelay}s`,
            animationIterationCount: "infinite",
            animationTimingFunction: "ease-in-out",
            willChange: "transform",
          }}
        />
      ))}
    </div>
  );
}

export default AnimatedBlobs;
