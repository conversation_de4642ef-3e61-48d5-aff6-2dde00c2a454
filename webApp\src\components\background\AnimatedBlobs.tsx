import { useEffect, useState } from "react";
import { useTheme } from "../../contexts";

interface Blob {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  borderRadius: string;
  color: string;
  opacity: number;
  animationDuration: number;
  animationDelay: number;
}

interface AnimatedBlobsProps {
  enabled: boolean;
  animated: boolean;
}

// Light theme colors - vibrant CSS gradients with various types
const LIGHT_COLORS = [
  // Linear gradients - diagonal
  "linear-gradient(135deg, rgba(99, 102, 241, 0.6), rgba(168, 85, 247, 0.6))", // indigo to violet
  "linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(236, 72, 153, 0.6))", // blue to pink
  "linear-gradient(135deg, rgba(139, 92, 246, 0.6), rgba(239, 68, 68, 0.6))", // violet to red
  "linear-gradient(135deg, rgba(16, 185, 129, 0.6), rgba(5, 150, 105, 0.6))", // emerald
  "linear-gradient(135deg, rgba(251, 191, 36, 0.6), rgba(245, 101, 101, 0.6))", // amber to red

  // Radial gradients
  "radial-gradient(circle, rgba(99, 102, 241, 0.6) 0%, rgba(168, 85, 247, 0.6) 100%)", // indigo to violet
  "radial-gradient(circle, rgba(34, 197, 94, 0.6) 0%, rgba(59, 130, 246, 0.6) 100%)", // green to blue
  "radial-gradient(circle, rgba(236, 72, 153, 0.6) 0%, rgba(251, 191, 36, 0.6) 100%)", // pink to amber

  // Conic gradients
  "conic-gradient(from 0deg, rgba(99, 102, 241, 0.6), rgba(168, 85, 247, 0.6), rgba(236, 72, 153, 0.6), rgba(99, 102, 241, 0.6))",
  "conic-gradient(from 45deg, rgba(16, 185, 129, 0.6), rgba(59, 130, 246, 0.6), rgba(139, 92, 246, 0.6), rgba(16, 185, 129, 0.6))",

  // Multi-stop linear gradients
  "linear-gradient(45deg, rgba(99, 102, 241, 0.6) 0%, rgba(168, 85, 247, 0.6) 50%, rgba(236, 72, 153, 0.6) 100%)",
  "linear-gradient(225deg, rgba(34, 197, 94, 0.6) 0%, rgba(59, 130, 246, 0.6) 50%, rgba(139, 92, 246, 0.6) 100%)",
];

// Dark theme colors - slightly lower opacity but still vibrant
const DARK_COLORS = [
  // Linear gradients - diagonal
  "linear-gradient(135deg, rgba(99, 102, 241, 0.4), rgba(168, 85, 247, 0.4))", // indigo to violet
  "linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(236, 72, 153, 0.4))", // blue to pink
  "linear-gradient(135deg, rgba(139, 92, 246, 0.4), rgba(239, 68, 68, 0.4))", // violet to red
  "linear-gradient(135deg, rgba(16, 185, 129, 0.4), rgba(5, 150, 105, 0.4))", // emerald
  "linear-gradient(135deg, rgba(251, 191, 36, 0.4), rgba(245, 101, 101, 0.4))", // amber to red

  // Radial gradients
  "radial-gradient(circle, rgba(99, 102, 241, 0.4) 0%, rgba(168, 85, 247, 0.4) 100%)", // indigo to violet
  "radial-gradient(circle, rgba(34, 197, 94, 0.4) 0%, rgba(59, 130, 246, 0.4) 100%)", // green to blue
  "radial-gradient(circle, rgba(236, 72, 153, 0.4) 0%, rgba(251, 191, 36, 0.4) 100%)", // pink to amber

  // Conic gradients
  "conic-gradient(from 0deg, rgba(99, 102, 241, 0.4), rgba(168, 85, 247, 0.4), rgba(236, 72, 153, 0.4), rgba(99, 102, 241, 0.4))",
  "conic-gradient(from 45deg, rgba(16, 185, 129, 0.4), rgba(59, 130, 246, 0.4), rgba(139, 92, 246, 0.4), rgba(16, 185, 129, 0.4))",

  // Multi-stop linear gradients
  "linear-gradient(45deg, rgba(99, 102, 241, 0.4) 0%, rgba(168, 85, 247, 0.4) 50%, rgba(236, 72, 153, 0.4) 100%)",
  "linear-gradient(225deg, rgba(34, 197, 94, 0.4) 0%, rgba(59, 130, 246, 0.4) 50%, rgba(139, 92, 246, 0.4) 100%)",
];

const generateRandomBlob = (isDark: boolean): Blob => {
  const colors = isDark ? DARK_COLORS : LIGHT_COLORS;

  // Generate irregular blob shapes
  const baseSize = Math.random() * 300 + 150; // 150-450px base
  const aspectRatio = Math.random() * 0.6 + 0.7; // 0.7-1.3 ratio

  // Create irregular border radius for organic shapes
  const borderRadiusValues = Array.from(
    { length: 8 },
    () => Math.random() * 60 + 20 // 20-80% values
  );
  const borderRadius = `${borderRadiusValues[0]}% ${borderRadiusValues[1]}% ${borderRadiusValues[2]}% ${borderRadiusValues[3]}% / ${borderRadiusValues[4]}% ${borderRadiusValues[5]}% ${borderRadiusValues[6]}% ${borderRadiusValues[7]}%`;

  return {
    id: Math.random().toString(36).substring(2, 11),
    x: Math.random() * 100,
    y: Math.random() * 100,
    width: baseSize,
    height: baseSize * aspectRatio,
    borderRadius,
    color: colors[Math.floor(Math.random() * colors.length)],
    opacity: Math.random() * 0.5 + 0.2, // 0.2-0.7 opacity - increased visibility
    animationDuration: Math.random() * 15 + 10, // 10-25 seconds - faster movement
    animationDelay: Math.random() * 3, // 0-3 seconds delay
  };
};

function AnimatedBlobs({ enabled, animated }: AnimatedBlobsProps) {
  const { isDarkMode } = useTheme();
  const [blobs, setBlobs] = useState<Blob[]>([]);

  useEffect(() => {
    if (enabled) {
      // Generate random number of blobs (4-8)
      const blobCount = Math.floor(Math.random() * 5) + 4;
      const newBlobs = Array.from({ length: blobCount }, () =>
        generateRandomBlob(isDarkMode)
      );
      setBlobs(newBlobs);
    } else {
      setBlobs([]);
    }
  }, [enabled, isDarkMode]);

  if (!enabled || blobs.length === 0) {
    return null;
  }

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {blobs.map((blob) => (
        <div
          key={blob.id}
          className={`absolute blur-3xl ${animated ? "animate-float" : ""}`}
          style={{
            left: `${blob.x}%`,
            top: `${blob.y}%`,
            width: `${blob.width}px`,
            height: `${blob.height}px`,
            background: blob.color,
            borderRadius: blob.borderRadius,
            opacity: blob.opacity,
            transform: "translate(-50%, -50%)",
            animationDuration: `${blob.animationDuration}s`,
            animationDelay: `${blob.animationDelay}s`,
            willChange: animated ? "transform" : "auto",
          }}
        />
      ))}
    </div>
  );
}

export default AnimatedBlobs;
