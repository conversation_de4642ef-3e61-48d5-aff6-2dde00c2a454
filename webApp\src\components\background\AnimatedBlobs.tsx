import { useEffect, useState } from "react";
import { useTheme } from "../../contexts";

interface Blob {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  borderRadius: string;
  colorClass: string;
  opacity: number;
  animationDuration: number;
  animationDelay: number;
}

interface AnimatedBlobsProps {
  enabled: boolean;
  animated: boolean;
}

// Light theme colors - Tailwind background classes for easy identification
const LIGHT_COLORS = [
  // Linear gradients
  "bg-gradient-indigo-violet", // indigo to violet diagonal
  "bg-gradient-blue-pink", // blue to pink diagonal
  "bg-gradient-violet-red", // violet to red diagonal
  "bg-gradient-emerald", // emerald gradient
  "bg-gradient-amber-red", // amber to red diagonal
  "bg-gradient-green-blue", // green to blue diagonal
  "bg-gradient-purple-orange", // purple to orange diagonal
  "bg-gradient-cyan-purple", // cyan to purple diagonal

  // Radial gradients
  "bg-radial-indigo-violet", // indigo to violet radial
  "bg-radial-green-blue", // green to blue radial
  "bg-radial-pink-amber", // pink to amber radial
  "bg-radial-purple-cyan", // purple to cyan radial

  // Conic gradients
  "bg-conic-rainbow", // rainbow conic gradient
  "bg-conic-nature", // nature colors conic
  "bg-conic-sunset", // sunset colors conic

  // Multi-stop gradients
  "bg-multi-purple", // multi-stop purple gradient
  "bg-multi-nature", // multi-stop nature gradient
  "bg-multi-warm", // multi-stop warm gradient
];

// Dark theme colors - same patterns with -dark suffix for lower opacity
const DARK_COLORS = [
  // Linear gradients
  "bg-gradient-indigo-violet-dark", // indigo to violet diagonal (dark)
  "bg-gradient-blue-pink-dark", // blue to pink diagonal (dark)
  "bg-gradient-violet-red-dark", // violet to red diagonal (dark)
  "bg-gradient-emerald-dark", // emerald gradient (dark)
  "bg-gradient-amber-red-dark", // amber to red diagonal (dark)
  "bg-gradient-green-blue-dark", // green to blue diagonal (dark)
  "bg-gradient-purple-orange-dark", // purple to orange diagonal (dark)
  "bg-gradient-cyan-purple-dark", // cyan to purple diagonal (dark)

  // Radial gradients
  "bg-radial-indigo-violet-dark", // indigo to violet radial (dark)
  "bg-radial-green-blue-dark", // green to blue radial (dark)
  "bg-radial-pink-amber-dark", // pink to amber radial (dark)
  "bg-radial-purple-cyan-dark", // purple to cyan radial (dark)

  // Conic gradients
  "bg-conic-rainbow-dark", // rainbow conic gradient (dark)
  "bg-conic-nature-dark", // nature colors conic (dark)
  "bg-conic-sunset-dark", // sunset colors conic (dark)

  // Multi-stop gradients
  "bg-multi-purple-dark", // multi-stop purple gradient (dark)
  "bg-multi-nature-dark", // multi-stop nature gradient (dark)
  "bg-multi-warm-dark", // multi-stop warm gradient (dark)
];

const generateRandomBlob = (isDark: boolean): Blob => {
  const colors = isDark ? DARK_COLORS : LIGHT_COLORS;

  // Generate irregular blob shapes
  const baseSize = Math.random() * 300 + 150; // 150-450px base
  const aspectRatio = Math.random() * 0.6 + 0.7; // 0.7-1.3 ratio

  // Create irregular border radius for organic shapes
  const borderRadiusValues = Array.from(
    { length: 8 },
    () => Math.random() * 60 + 20 // 20-80% values
  );
  const borderRadius = `${borderRadiusValues[0]}% ${borderRadiusValues[1]}% ${borderRadiusValues[2]}% ${borderRadiusValues[3]}% / ${borderRadiusValues[4]}% ${borderRadiusValues[5]}% ${borderRadiusValues[6]}% ${borderRadiusValues[7]}%`;

  return {
    id: Math.random().toString(36).substring(2, 11),
    x: Math.random() * 100,
    y: Math.random() * 100,
    width: baseSize,
    height: baseSize * aspectRatio,
    borderRadius,
    colorClass: colors[Math.floor(Math.random() * colors.length)],
    opacity: Math.random() * 0.4 + 0.6, // 0.6-1.0 opacity - much more visible
    animationDuration: Math.random() * 15 + 10, // 10-25 seconds - faster movement
    animationDelay: Math.random() * 3, // 0-3 seconds delay
  };
};

function AnimatedBlobs({ enabled, animated }: AnimatedBlobsProps) {
  const { isDarkMode } = useTheme();
  const [blobs, setBlobs] = useState<Blob[]>([]);

  useEffect(() => {
    if (enabled) {
      // Generate random number of blobs (4-8)
      const blobCount = Math.floor(Math.random() * 5) + 4;
      const newBlobs = Array.from({ length: blobCount }, () =>
        generateRandomBlob(isDarkMode)
      );
      setBlobs(newBlobs);
    } else {
      setBlobs([]);
    }
  }, [enabled, isDarkMode]);

  if (!enabled || blobs.length === 0) {
    return null;
  }

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {blobs.map((blob) => (
        <div
          key={blob.id}
          className={`absolute blur-3xl ${blob.colorClass} ${
            animated ? "animate-float" : ""
          }`}
          style={{
            left: `${blob.x}%`,
            top: `${blob.y}%`,
            width: `${blob.width}px`,
            height: `${blob.height}px`,
            borderRadius: blob.borderRadius,
            opacity: blob.opacity,
            transform: "translate(-50%, -50%)",
            animationDuration: `${blob.animationDuration}s`,
            animationDelay: `${blob.animationDelay}s`,
            willChange: animated ? "transform" : "auto",
          }}
        />
      ))}
    </div>
  );
}

export default AnimatedBlobs;
