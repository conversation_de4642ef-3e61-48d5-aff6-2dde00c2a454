import { useEffect, useState } from "react";
import { useTheme } from "../../contexts";

interface Blob {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  borderRadius: string;
  color: string;
  opacity: number;
  animationDuration: number;
  animationDelay: number;
}

interface AnimatedBlobsProps {
  enabled: boolean;
  animated: boolean;
}

// Light theme colors - increased opacity for better visibility
const LIGHT_COLORS = [
  "linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(168, 85, 247, 0.3))",
  "linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3))",
  "linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(219, 39, 119, 0.3))",
  "linear-gradient(135deg, rgba(79, 70, 229, 0.3), rgba(124, 58, 237, 0.3))",
  "linear-gradient(135deg, rgba(67, 56, 202, 0.3), rgba(109, 40, 217, 0.3))",
];

// Dark theme colors
const DARK_COLORS = [
  "linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(168, 85, 247, 0.2))",
  "linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2))",
  "linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(219, 39, 119, 0.2))",
  "linear-gradient(135deg, rgba(79, 70, 229, 0.2), rgba(124, 58, 237, 0.2))",
  "linear-gradient(135deg, rgba(67, 56, 202, 0.2), rgba(109, 40, 217, 0.2))",
];

const generateRandomBlob = (isDark: boolean): Blob => {
  const colors = isDark ? DARK_COLORS : LIGHT_COLORS;

  // Generate irregular blob shapes
  const baseSize = Math.random() * 300 + 150; // 150-450px base
  const aspectRatio = Math.random() * 0.6 + 0.7; // 0.7-1.3 ratio

  // Create irregular border radius for organic shapes
  const borderRadiusValues = Array.from(
    { length: 8 },
    () => Math.random() * 60 + 20 // 20-80% values
  );
  const borderRadius = `${borderRadiusValues[0]}% ${borderRadiusValues[1]}% ${borderRadiusValues[2]}% ${borderRadiusValues[3]}% / ${borderRadiusValues[4]}% ${borderRadiusValues[5]}% ${borderRadiusValues[6]}% ${borderRadiusValues[7]}%`;

  return {
    id: Math.random().toString(36).substring(2, 11),
    x: Math.random() * 100,
    y: Math.random() * 100,
    width: baseSize,
    height: baseSize * aspectRatio,
    borderRadius,
    color: colors[Math.floor(Math.random() * colors.length)],
    opacity: Math.random() * 0.4 + 0.1, // 0.1-0.5 opacity
    animationDuration: Math.random() * 20 + 15, // 15-35 seconds
    animationDelay: Math.random() * 5, // 0-5 seconds delay
  };
};

function AnimatedBlobs({ enabled, animated }: AnimatedBlobsProps) {
  const { mode } = useTheme();
  const [blobs, setBlobs] = useState<Blob[]>([]);

  useEffect(() => {
    if (enabled) {
      // Generate random number of blobs (4-8)
      const blobCount = Math.floor(Math.random() * 5) + 4;
      const newBlobs = Array.from({ length: blobCount }, () =>
        generateRandomBlob(mode === "dark")
      );
      setBlobs(newBlobs);
    } else {
      setBlobs([]);
    }
  }, [enabled, mode]);

  if (!enabled || blobs.length === 0) {
    return null;
  }

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {blobs.map((blob) => (
        <div
          key={blob.id}
          className={`absolute blur-3xl ${animated ? "animate-float" : ""}`}
          style={{
            left: `${blob.x}%`,
            top: `${blob.y}%`,
            width: `${blob.width}px`,
            height: `${blob.height}px`,
            background: blob.color,
            borderRadius: blob.borderRadius,
            opacity: blob.opacity,
            transform: "translate(-50%, -50%)",
            animationDuration: `${blob.animationDuration}s`,
            animationDelay: `${blob.animationDelay}s`,
            willChange: animated ? "transform" : "auto",
          }}
        />
      ))}
    </div>
  );
}

export default AnimatedBlobs;
