import {
  Row,
  Col,
  Card,
  <PERSON>atistic,
  Typography,
  But<PERSON>,
  List,
  Tag,
  Space,
} from "antd";
import {
  FileTextOutlined,
  PlusCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "../../common/routes";
import { useAuth } from "../../contexts";

const { Title, Text } = Typography;

// Mock data for dashboard
const mockStats = {
  totalFiles: 24,
  pendingFiles: 8,
  completedFiles: 16,
  thisMonthFiles: 12,
};

const mockRecentFiles = [
  {
    id: "1",
    name: "Tax Return 2024",
    status: "pending",
    date: "2024-01-15",
    type: "Tax Document",
  },
  {
    id: "2",
    name: "Business License Renewal",
    status: "completed",
    date: "2024-01-14",
    type: "License",
  },
  {
    id: "3",
    name: "Annual Report",
    status: "in-progress",
    date: "2024-01-13",
    type: "Report",
  },
  {
    id: "4",
    name: "Permit Application",
    status: "pending",
    date: "2024-01-12",
    type: "Permit",
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "completed":
      return "success";
    case "pending":
      return "warning";
    case "in-progress":
      return "processing";
    default:
      return "default";
  }
};

function Dashboard() {
  // HOOKS
  const navigate = useNavigate();
  const { user } = useAuth();

  // EVENT HANDLERS
  const handleCreateNewFile = () => {
    navigate(ROUTES.CREATE_NEW_FILE);
  };

  const handleViewAllFiles = () => {
    navigate(ROUTES.MY_FILES);
  };

  // RENDER LOGIC
  const welcomeMessage = `Welcome back, ${user?.name || "User"}!`;

  return (
    <div>
      <div className="mb-6">
        <Title level={2}>{welcomeMessage}</Title>
        <Text className="text-gray-600">
          Here's an overview of your e-filing activities
        </Text>
      </div>

      {/* Statistics Cards with Glass Morphism */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="backdrop-blur-md bg-white/20 dark:bg-black/20 border-white/30 dark:border-white/10"
            styles={{ body: { padding: "20px" } }}
          >
            <Statistic
              title="Total Files"
              value={mockStats.totalFiles}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="backdrop-blur-md bg-white/20 dark:bg-black/20 border-white/30 dark:border-white/10"
            styles={{ body: { padding: "20px" } }}
          >
            <Statistic
              title="Pending Files"
              value={mockStats.pendingFiles}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: "#faad14" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="backdrop-blur-md bg-white/20 dark:bg-black/20 border-white/30 dark:border-white/10"
            styles={{ body: { padding: "20px" } }}
          >
            <Statistic
              title="Completed Files"
              value={mockStats.completedFiles}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: "#52c41a" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="backdrop-blur-md bg-white/20 dark:bg-black/20 border-white/30 dark:border-white/10"
            styles={{ body: { padding: "20px" } }}
          >
            <Statistic
              title="This Month"
              value={mockStats.thisMonthFiles}
              prefix={<ArrowUpOutlined />}
              valueStyle={{ color: "#722ed1" }}
            />
          </Card>
        </Col>
      </Row>

      {/* Quick Actions and Recent Files with Glass Morphism */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={8}>
          <Card
            title="Quick Actions"
            className="h-full backdrop-blur-md bg-white/20 dark:bg-black/20 border-white/30 dark:border-white/10"
            styles={{ body: { padding: "24px" } }}
          >
            <Space direction="vertical" className="w-full" size="middle">
              <Button
                type="primary"
                icon={<PlusCircleOutlined />}
                onClick={handleCreateNewFile}
                className="w-full"
                size="large"
              >
                Create New File
              </Button>
              <Button
                icon={<FileTextOutlined />}
                onClick={handleViewAllFiles}
                className="w-full"
                size="large"
              >
                View All Files
              </Button>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={16}>
          <Card
            title="Recent Files"
            extra={
              <Button type="link" onClick={handleViewAllFiles}>
                View All
              </Button>
            }
            className="h-full backdrop-blur-md bg-white/20 dark:bg-black/20 border-white/30 dark:border-white/10"
            styles={{ body: { padding: "24px" } }}
          >
            <List
              dataSource={mockRecentFiles}
              renderItem={(file) => (
                <List.Item>
                  <List.Item.Meta
                    title={file.name}
                    description={
                      <Text type="secondary">
                        {file.type} • {file.date}
                      </Text>
                    }
                  />
                  <Tag color={getStatusColor(file.status)}>
                    {file.status.replace("-", " ").toUpperCase()}
                  </Tag>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default Dashboard;
