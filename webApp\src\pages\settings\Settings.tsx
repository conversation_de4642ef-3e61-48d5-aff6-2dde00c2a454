import { Card, Switch, Typography, Divider, Space } from "antd";
import { useTheme, useBackground } from "../../contexts";

const { Title, Text } = Typography;

function Settings() {
  // HOOKS
  const { isDarkMode, toggleTheme } = useTheme();
  const { settings, toggleAnimations, toggleBlobs, toggleArtwork } =
    useBackground();

  return (
    <div>
      <Title level={2}>Settings</Title>
      <Text className="text-gray-600 mb-6 block">
        Customize your application preferences
      </Text>

      {/* Theme Settings */}
      <Card title="Theme Settings" className="mb-6">
        <Space direction="vertical" className="w-full">
          <div className="flex items-center justify-between">
            <div>
              <Text strong>Dark Mode</Text>
              <br />
              <Text type="secondary">Switch between light and dark themes</Text>
            </div>
            <Switch
              checked={isDarkMode}
              onChange={toggleTheme}
              checkedChildren="🌙"
              unCheckedChildren="☀️"
            />
          </div>
        </Space>
      </Card>

      {/* Background Settings */}
      <Card title="Background Effects" className="mb-6">
        <Space direction="vertical" className="w-full" size="large">
          <div className="flex items-center justify-between">
            <div>
              <Text strong>Animated Blobs</Text>
              <br />
              <Text type="secondary">
                Show animated gradient blobs in the background
              </Text>
            </div>
            <Switch checked={settings.blobsEnabled} onChange={toggleBlobs} />
          </div>

          <Divider />

          <div className="flex items-center justify-between">
            <div>
              <Text strong>Background Artwork</Text>
              <br />
              <Text type="secondary">
                Display e-filing themed icons in the background
              </Text>
            </div>
            <Switch
              checked={settings.artworkEnabled}
              onChange={toggleArtwork}
            />
          </div>

          <Divider />

          <div className="flex items-center justify-between">
            <div>
              <Text strong>Animations</Text>
              <br />
              <Text type="secondary">
                Enable smooth animations for background elements
              </Text>
            </div>
            <Switch
              checked={settings.animationsEnabled}
              onChange={toggleAnimations}
            />
          </div>
        </Space>
      </Card>

      {/* Performance Note */}
      <Card>
        <Text type="secondary">
          <strong>Note:</strong> Disabling animations can improve performance on
          slower devices. Background effects are optimized for modern browsers
          and should not significantly impact performance.
        </Text>
      </Card>
    </div>
  );
}

export default Settings;
