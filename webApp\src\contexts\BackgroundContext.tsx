import { createContext, useContext, useState, useEffect, type ReactNode } from 'react';

interface BackgroundSettings {
  animationsEnabled: boolean;
  blobsEnabled: boolean;
  artworkEnabled: boolean;
}

interface BackgroundContextType {
  settings: BackgroundSettings;
  updateSettings: (newSettings: Partial<BackgroundSettings>) => void;
  toggleAnimations: () => void;
  toggleBlobs: () => void;
  toggleArtwork: () => void;
}

const BackgroundContext = createContext<BackgroundContextType | undefined>(undefined);

interface BackgroundProviderProps {
  children: ReactNode;
}

const BACKGROUND_STORAGE_KEY = 'e-filing-background-settings';

const DEFAULT_SETTINGS: BackgroundSettings = {
  animationsEnabled: true,
  blobsEnabled: true,
  artworkEnabled: true,
};

export function BackgroundProvider({ children }: BackgroundProviderProps) {
  const [settings, setSettings] = useState<BackgroundSettings>(DEFAULT_SETTINGS);

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem(BACKGROUND_STORAGE_KEY);
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...DEFAULT_SETTINGS, ...parsed });
      } catch (error) {
        console.error('Error parsing background settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage when they change
  useEffect(() => {
    localStorage.setItem(BACKGROUND_STORAGE_KEY, JSON.stringify(settings));
  }, [settings]);

  const updateSettings = (newSettings: Partial<BackgroundSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const toggleAnimations = () => {
    setSettings(prev => ({ ...prev, animationsEnabled: !prev.animationsEnabled }));
  };

  const toggleBlobs = () => {
    setSettings(prev => ({ ...prev, blobsEnabled: !prev.blobsEnabled }));
  };

  const toggleArtwork = () => {
    setSettings(prev => ({ ...prev, artworkEnabled: !prev.artworkEnabled }));
  };

  const value: BackgroundContextType = {
    settings,
    updateSettings,
    toggleAnimations,
    toggleBlobs,
    toggleArtwork,
  };

  return (
    <BackgroundContext.Provider value={value}>
      {children}
    </BackgroundContext.Provider>
  );
}

export function useBackground(): BackgroundContextType {
  const context = useContext(BackgroundContext);
  if (context === undefined) {
    throw new Error('useBackground must be used within a BackgroundProvider');
  }
  return context;
}
