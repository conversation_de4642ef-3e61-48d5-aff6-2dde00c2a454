import { Layout, Typography, <PERSON>, Button } from "antd";
import { SunOutlined, MoonOutlined } from "@ant-design/icons";
import { Outlet } from "react-router-dom";
import { useTheme } from "../contexts";

const { Content } = Layout;
const { Title, Text } = Typography;

function AuthLayout() {
  const { toggleTheme, isDarkMode } = useTheme();

  return (
    <Layout className="!min-h-screen !bg-transparent">
      <Button
        type="text"
        icon={isDarkMode ? <SunOutlined /> : <MoonOutlined />}
        onClick={toggleTheme}
        className="text-lg"
      />

      <Content className="flex items-center justify-center p-4 min-h-screen">
        <div className="w-full max-w-md">
          {/* Logo/Brand Section */}
          <div className="text-center mb-8">
            <Title level={1} className="text-blue-600 mb-2">
              E-Filing
            </Title>
            <Text className="text-gray-600">
              Streamline your document filing process
            </Text>
          </div>

          {/* Auth Form Container with Glass Morphism */}
          <Card
            className={`${
              isDarkMode
                ? "shadow-md shadow-violet-950 glass-morphism-dark"
                : "shadow-md shadow-gray-300 glass-morphism"
            }`}
            styles={{ body: { padding: "32px" } }}
          >
            <Outlet />
          </Card>

          {/* Footer */}
          <div className="text-center mt-6">
            <Text className="text-gray-500 text-sm">
              © 2024 E-Filing System. All rights reserved.
            </Text>
          </div>
        </div>
      </Content>
    </Layout>
  );
}

export default AuthLayout;
