import { lazy } from 'react';
import { ROUTES } from '../common/routes';

// Lazy load all components
const Login = lazy(() => import('../pages/login').then(module => ({ default: module.Login })));
const Signup = lazy(() => import('../pages/signup').then(module => ({ default: module.Signup })));
const Dashboard = lazy(() => import('../pages/dashboard').then(module => ({ default: module.Dashboard })));
const MyFiles = lazy(() => import('../pages/myFiles').then(module => ({ default: module.MyFiles })));
const CreateNewFile = lazy(() => import('../pages/createNewFile').then(module => ({ default: module.CreateNewFile })));
const TrackFiles = lazy(() => import('../pages/trackFiles').then(module => ({ default: module.TrackFiles })));
const Settings = lazy(() => import('../pages/settings').then(module => ({ default: module.Settings })));

// Auth routes configuration
export const authRoutes = [
  {
    path: ROUTES.LOGIN,
    component: Login,
    layout: 'auth' as const,
  },
  {
    path: ROUTES.SIGNUP,
    component: Signup,
    layout: 'auth' as const,
  },
];

// Protected routes configuration
export const protectedRoutes = [
  {
    path: ROUTES.DASHBOARD,
    component: Dashboard,
    layout: 'app' as const,
  },
  {
    path: ROUTES.MY_FILES,
    component: MyFiles,
    layout: 'app' as const,
  },
  {
    path: ROUTES.CREATE_NEW_FILE,
    component: CreateNewFile,
    layout: 'app' as const,
  },
  {
    path: ROUTES.TRACK_FILES,
    component: TrackFiles,
    layout: 'app' as const,
  },
  {
    path: ROUTES.SETTINGS,
    component: Settings,
    layout: 'app' as const,
  },
];

// All routes combined
export const allRoutes = [...authRoutes, ...protectedRoutes];