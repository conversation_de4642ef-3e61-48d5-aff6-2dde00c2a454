import { type ReactNode } from 'react';
import {
  DashboardOutlined,
  FileTextOutlined,
  PlusCircleOutlined,
  SearchOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { ROUTES } from './routes';

export interface MenuItem {
  key: string;
  icon: ReactNode;
  label: string;
  path: string;
  description?: string;
}

export const MENU_ITEMS: MenuItem[] = [
  {
    key: 'dashboard',
    icon: <DashboardOutlined />,
    label: 'Dashboard',
    path: ROUTES.DASHBOARD,
    description: 'Overview of your e-filing activities',
  },
  {
    key: 'my-files',
    icon: <FileTextOutlined />,
    label: 'My Files',
    path: ROUTES.MY_FILES,
    description: 'Manage your filed documents',
  },
  {
    key: 'create-new-file',
    icon: <PlusCircleOutlined />,
    label: 'Create New File',
    path: ROUTES.CREATE_NEW_FILE,
    description: 'Start a new filing process',
  },
  {
    key: 'track-files',
    icon: <SearchOutlined />,
    label: 'Track Files',
    path: ROUTES.TRACK_FILES,
    description: 'Monitor your filing status',
  },
];

// Settings is handled separately in the user menu
export const SETTINGS_MENU_ITEM: MenuItem = {
  key: 'settings',
  icon: <SettingOutlined />,
  label: 'Settings',
  path: ROUTES.SETTINGS,
  description: 'Customize your preferences',
};

// Convert menu items to Antd menu format
export const getAntdMenuItems = () => {
  return MENU_ITEMS.map(item => ({
    key: item.path,
    icon: item.icon,
    label: item.label,
  }));
};
