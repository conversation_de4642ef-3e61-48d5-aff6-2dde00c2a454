import {
  createContext,
  useContext,
  useState,
  useEffect,
  type ReactNode,
} from "react";
import { App } from "antd";

interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, name: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

// Mock user data
const MOCK_USER: User = {
  id: "1",
  email: "<EMAIL>",
  name: "<PERSON>",
  avatar: undefined,
};

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { message } = App.useApp();

  // Check for existing session on mount
  useEffect(() => {
    const checkAuth = () => {
      const savedUser = localStorage.getItem("auth_user");
      if (savedUser) {
        try {
          setUser(JSON.parse(savedUser));
        } catch (error) {
          console.error("Error parsing saved user:", error);
          localStorage.removeItem("auth_user");
        }
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    setIsLoading(true);

    try {
      // Mock API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Accept any email and password for demo
      if (email && password) {
        const loggedInUser = { ...MOCK_USER, email };
        setUser(loggedInUser);
        localStorage.setItem("auth_user", JSON.stringify(loggedInUser));
        message.success("Login successful!");
      } else {
        throw new Error("Please enter both email and password");
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : "Login failed");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (
    email: string,
    _password: string,
    name: string
  ): Promise<void> => {
    setIsLoading(true);

    try {
      // Mock API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Mock user creation
      const newUser: User = {
        id: Date.now().toString(),
        email,
        name,
      };

      setUser(newUser);
      localStorage.setItem("auth_user", JSON.stringify(newUser));
      message.success("Account created successfully!");
    } catch (error) {
      message.error(error instanceof Error ? error.message : "Signup failed");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem("auth_user");
    message.success("Logged out successfully");
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    signup,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
