import { App as AntdApp } from "antd";
import { Router } from "./router";
import { Theme<PERSON><PERSON><PERSON>, AuthProvider, BackgroundProvider } from "./contexts";
import { BackgroundSystem } from "./components";

function App() {
  return (
    <ThemeProvider>
      <BackgroundProvider>
        <AuthProvider>
          <AntdApp>
            <BackgroundSystem>
              <Router />
            </BackgroundSystem>
          </AntdApp>
        </AuthProvider>
      </BackgroundProvider>
    </ThemeProvider>
  );
}

export default App;
