import { useState } from "react";
import {
  Layout,
  <PERSON>u,
  Button,
  Typography,
  Avatar,
  Dropdown,
  Space,
} from "antd";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  SunOutlined,
  MoonOutlined,
} from "@ant-design/icons";
import { type ReactNode } from "react";
import { useNavigate, useLocation, Outlet } from "react-router-dom";
import { ROUTES } from "../common/routes";
import { getAntdMenuItems } from "../common/menu";
import { useAuth, useTheme } from "../contexts";

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const menuItems = getAntdMenuItems();

interface AppLayoutProps {
  children?: ReactNode;
}

function AppLayout({ children }: AppLayoutProps) {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const { mode, toggleTheme } = useTheme();

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const handleLogout = () => {
    logout();
    navigate(ROUTES.LOGIN);
  };

  const userMenuItems = [
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: "Settings",
      onClick: () => navigate(ROUTES.SETTINGS),
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: "Logout",
      onClick: handleLogout,
    },
  ];

  return (
    <Layout className="!min-h-screen !bg-transparent">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          background:
            mode === "dark" ? "rgba(0, 0, 0, 0.8)" : "rgba(255, 255, 255, 0.8)",
          backdropFilter: "blur(10px)",
          boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
        }}
      >
        <div className="p-4 text-center border-b">
          <Text strong className="text-blue-600">
            {collapsed ? "EF" : "E-Filing"}
          </Text>
        </div>
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="border-r-0"
        />
      </Sider>

      <Layout>
        <Header
          className="px-4 shadow-sm flex items-center justify-between"
          style={{
            background:
              mode === "dark"
                ? "rgba(0, 0, 0, 0.8)"
                : "rgba(255, 255, 255, 0.8)",
            backdropFilter: "blur(10px)",
          }}
        >
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            className="text-lg"
          />

          <Space>
            <Button
              type="text"
              icon={mode === "dark" ? <SunOutlined /> : <MoonOutlined />}
              onClick={toggleTheme}
              className="text-lg"
            />
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Space className="cursor-pointer">
                <Avatar icon={<UserOutlined />} />
                <Text>{user?.name || "User"}</Text>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        <Content
          className="m-6 p-6 rounded-lg shadow-lg"
          style={{
            background:
              mode === "dark"
                ? "rgba(0, 0, 0, 0.1)"
                : "rgba(255, 255, 255, 0.1)",
            backdropFilter: "blur(10px)",
            border:
              mode === "dark"
                ? "1px solid rgba(255, 255, 255, 0.1)"
                : "1px solid rgba(255, 255, 255, 0.2)",
          }}
        >
          {children || <Outlet />}
        </Content>
      </Layout>
    </Layout>
  );
}

export default AppLayout;
