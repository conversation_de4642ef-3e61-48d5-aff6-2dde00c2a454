import { type ReactNode } from "react";
import { useBackground } from "../../contexts/BackgroundContext";
import AnimatedBlobs from "./AnimatedBlobs";
import BackgroundArtwork from "./BackgroundArtwork";

import { theme } from "antd";

interface BackgroundSystemProps {
  children: ReactNode;
}

function BackgroundSystem({ children }: BackgroundSystemProps) {
  const { settings } = useBackground();

  const { token } = theme.useToken();

  return (
    <div
      className={`relative min-h-screen `}
      style={{
        background: token.colorBgContainer,
      }}
    >
      {/* Animated background blobs */}
      <AnimatedBlobs
        enabled={settings.blobsEnabled}
        animated={settings.animationsEnabled}
      />

      {/* Static background artwork */}
      <BackgroundArtwork enabled={settings.artworkEnabled} />

      {/* Main content with glass morphism backdrop */}
      <div className="relative z-10">{children}</div>
    </div>
  );
}

export default BackgroundSystem;
